{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 17859023586365337561, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 7797225855875416119], [1260461579271933187, "serialize_to_javascript", false, 11282167472556839714], [1967864351173319501, "muda", false, 17721138419061163425], [2013030631243296465, "webview2_com", false, 18250755667997916887], [3331586631144870129, "getrandom", false, 12369738538365428214], [4143744114649553716, "raw_window_handle", false, 14790246364505868691], [4352886507220678900, "serde_json", false, 108169988327864308], [4537297827336760846, "thiserror", false, 8699437905799808735], [5404511084185685755, "url", false, 5908014307577041281], [6537120525306722933, "tauri_macros", false, 4097437604384204543], [6803352382179706244, "percent_encoding", false, 54158199049274140], [8427153362654230442, "build_script_build", false, 406349438730838672], [9010263965687315507, "http", false, 13184972111510878777], [9293239362693504808, "glob", false, 9378658457654588425], [9689903380558560274, "serde", false, 8940050712156267858], [9952368442187680820, "tauri_runtime_wry", false, 3170035825069153677], [10229185211513642314, "mime", false, 12894254746328959830], [11207653606310558077, "anyhow", false, 10727761341698528648], [11989259058781683633, "dunce", false, 8342769553454248279], [12565293087094287914, "window_vibrancy", false, 8019473905489104506], [12986574360607194341, "serde_repr", false, 5200848696445490038], [13066042571740262168, "log", false, 9752324521076004568], [13077543566650298139, "heck", false, 7681329073524019841], [14585479307175734061, "windows", false, 44454078113342564], [16727543399706004146, "cookie", false, 10648612767976272956], [16928111194414003569, "dirs", false, 2892600652581390562], [17233053221795943287, "tauri_utils", false, 12775440983760448248], [17531218394775549125, "tokio", false, 16237252703923316062], [18010483002580779355, "tauri_runtime", false, 8999434845449115140]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-2e9081e674e964a6\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}