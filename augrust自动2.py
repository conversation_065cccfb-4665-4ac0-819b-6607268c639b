import sys
from PyQt5.QtWidgets import (
    QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, QLabel, QLineEdit,
    QPushButton, QCheckBox, QHBoxLayout, QRadioButton
)
from PyQt5.QtGui import QFont, QMouseEvent
from PyQt5.QtCore import pyqtSlot, QTimer, QDateTime, QTime, Qt
import pyperclip


# 新增一个自动粘贴的 QLineEdit 子类
class AutoPasteLineEdit(QLineEdit):
    def __init__(self, parent=None):
        super().__init__(parent)

    def mousePressEvent(self, event: QMouseEvent):
        if event.button() == Qt.LeftButton:
            clipboard_text = pyperclip.paste()
            if clipboard_text:
                self.setText(clipboard_text)
        super().mousePressEvent(event)


# 以下是您的 CalculatorUI 类
class CalculatorUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.clipboard_automatic = True
        self.current_mode = 'AI'  # 默认模式为 AI
        self.timer_started = False
        self.start_time = QTime()

    def initUI(self):
        self.setWindowTitle('AI/AQ_Ktool_V3.4')
        self.setGeometry(1646, 363, 400, 600)

        self.font_large = QFont('Arial', 13)
        self.font_regular = QFont('Arial', 13)

        widget = QWidget()
        self.layout = QVBoxLayout()

        self.time_display = QLabel(self)
        self.time_display.setFont(self.font_large)

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_clock)
        self.timer.start(1000)

        ai_radio_button = QRadioButton("AI", self)
        ai_radio_button.setFont(self.font_large)
        aq_radio_button = QRadioButton("AQ", self)
        aq_radio_button.setFont(self.font_large)
        ai_radio_button.setChecked(True)

        radio_layout = QHBoxLayout()
        radio_layout.addWidget(ai_radio_button)
        radio_layout.addWidget(aq_radio_button)

        ai_radio_button.toggled.connect(self.on_radio_changed)

        self.analog_input = AutoPasteLineEdit(self)
        self.analog_input.setFont(self.font_large)
        self.k_input = AutoPasteLineEdit(self)
        self.k_input.setFont(self.font_large)
        self.b_input = AutoPasteLineEdit(self)
        self.b_input.setFont(self.font_large)

        # "set_value"输入框不使用自动粘贴
        self.setvalue_input = QLineEdit(self)
        self.setvalue_input.setFont(self.font_large)
        self.setvalue_input.setPlaceholderText("电流:18.008ma 电压0-5：3.001v__0-10：8.001v")

        label_analog_value = QLabel('初始模块值:', self)
        label_analog_value.setFont(self.font_large)
        label_b_value = QLabel('b_value:', self)
        label_b_value.setFont(self.font_large)
        label_k_value = QLabel('k_value:', self)
        label_k_value.setFont(self.font_large)
        label_set_value = QLabel('set_value:', self)
        label_set_value.setFont(self.font_large)

        self.prompt_display = QLabel('计算结果显示在这里:', self)
        self.prompt_display.setFont(self.font_large)
        self.result_display = QLabel('Result: ', self)
        self.result_display.setFont(self.font_large)
        self.clipboard_check = QCheckBox("自动复制结果", self)
        self.clipboard_check.setFont(self.font_large)
        self.clipboard_check.setChecked(True)
        self.clipboard_check.stateChanged.connect(self.toggle_clipboard)

        self.calc_button = QPushButton('计算', self)
        self.calc_button.setFont(self.font_large)
        self.calc_button.clicked.connect(self.on_calculate)

        self.font_size_input = QLineEdit(self)
        self.font_size_input.setPlaceholderText('输入字体大小')
        self.font_size_input.setFont(self.font_regular)
        self.font_size_button = QPushButton('设置字体大小默认13', self)
        self.font_size_button.setFont(self.font_regular)
        self.font_size_button.clicked.connect(self.set_font_size)

        self.stopwatch_display = QLabel('计 时 器 : 00:00:00.000', self)
        self.stopwatch_display.setFont(self.font_large)
        self.stopwatch_display.setAlignment(Qt.AlignLeft)

        self.stopwatch_button = QPushButton('开始计时', self)
        self.stopwatch_button.setFont(self.font_large)
        self.stopwatch_button.clicked.connect(self.start_stopwatch)

        self.stopwatch_timer = QTimer(self)
        self.stopwatch_timer.timeout.connect(self.update_stopwatch)

        self.layout.addLayout(radio_layout)
        self.layout.addWidget(label_analog_value)
        self.layout.addWidget(self.analog_input)
        self.layout.addWidget(label_b_value)
        self.layout.addWidget(self.b_input)
        self.layout.addWidget(label_k_value)
        self.layout.addWidget(self.k_input)
        self.layout.addWidget(label_set_value)
        self.layout.addWidget(self.setvalue_input)
        self.layout.addWidget(self.prompt_display)
        self.layout.addWidget(self.result_display)
        self.layout.addWidget(self.clipboard_check)
        self.layout.addWidget(self.calc_button)
        self.layout.addWidget(self.time_display)
        self.layout.addWidget(self.stopwatch_display)
        self.layout.addWidget(self.stopwatch_button)

        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(self.font_size_input)
        font_size_layout.addWidget(self.font_size_button)
        self.layout.addLayout(font_size_layout)

        widget.setLayout(self.layout)
        self.setCentralWidget(widget)

    @pyqtSlot()
    def set_font_size(self):
        try:
            size = int(self.font_size_input.text())
            font = QFont('Arial', size)
            self.analog_input.setFont(font)
            self.k_input.setFont(font)
            self.b_input.setFont(font)
            self.setvalue_input.setFont(font)
        except ValueError:
            self.font_size_input.setText('无效大小!')

    def update_clock(self):
        current_time = QDateTime.currentDateTime().toString('yyyy-MM-dd HH:mm:ss')
        self.time_display.setText(f"当前时间: {current_time}")

    def start_stopwatch(self):
        if not self.timer_started:
            self.timer_started = True
            self.start_time.start()
            self.stopwatch_timer.start(10)
            self.stopwatch_button.setText('停止计时')
        else:
            self.timer_started = False
            self.stopwatch_timer.stop()
            self.stopwatch_button.setText('开始计时')
            self.stopwatch_display.setText('计 时 器 : 00:00:00.000')

    def update_stopwatch(self):
        if self.timer_started:
            elapsed = self.start_time.elapsed()
            self.stopwatch_display.setText(f"计时器: {QTime(0, 0, 0, 0).addMSecs(elapsed).toString('hh:mm:ss.zzz')}")

    @pyqtSlot(bool)
    def on_radio_changed(self, enabled):
        if enabled:
            self.current_mode = 'AI'
            self.b_input.show()
        else:
            self.current_mode = 'AQ'
            self.b_input.hide()

    @pyqtSlot()
    def toggle_clipboard(self):
        self.clipboard_automatic = self.clipboard_check.isChecked()

    @pyqtSlot()
    def on_calculate(self):
        if self.current_mode == 'AI':
            self.on_calc_ai()
        else:
            self.on_calc_aq()

    def display_result(self, value):
        self.result_display.setText(f'Result: {value}')
        if self.clipboard_automatic:
            pyperclip.copy(str(value))

    def on_calc_ai(self):
        try:
            Analog = float(self.analog_input.text())
            k = float(self.k_input.text())
            b = float(self.b_input.text())
            SetValue = float(self.setvalue_input.text())
            x = (Analog - b) / k
            k1 = (SetValue - b) / x
            k3 = (2 * k) - k1

            value = round(k3, 4)
            self.display_result(value)
        except ValueError:
            self.analog_input.setText('无效输入!')
            self.result_display.setText('Result: ')
        finally:  # 无论是否发生异常，都会执行以下清除操作
            self.analog_input.clear()
            self.b_input.clear()

    def on_calc_aq(self):
        try:
            Analog = float(self.analog_input.text())
            k = float(self.k_input.text())
            SetValue = float(self.setvalue_input.text())
            x = Analog / k
            k9 = SetValue / x

            value = round(k9, 6)
            self.display_result(value)
        except ValueError:
            self.analog_input.setText('无效输入!')
            self.result_display.setText('Result: ')
        finally:  # 无论是否发生异常，都会执行以下清除操作
            self.analog_input.clear()
            self.b_input.clear()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = CalculatorUI()
    ex.show()
    sys.exit(app.exec_())
