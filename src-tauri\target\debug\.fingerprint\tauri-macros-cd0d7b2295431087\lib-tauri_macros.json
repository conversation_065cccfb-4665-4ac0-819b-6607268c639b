{"rustc": 3062648155896360161, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 15495850009162146506, "deps": [[373107762698212489, "proc_macro2", false, 17105446800078890304], [3412097196613774653, "tauri_codegen", false, 870235706070573207], [13077543566650298139, "heck", false, 18347787718723399560], [17233053221795943287, "tauri_utils", false, 14556261824533735700], [17332570067994900305, "syn", false, 2706061181772389358], [17990358020177143287, "quote", false, 15717735471653760071]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-cd0d7b2295431087\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}