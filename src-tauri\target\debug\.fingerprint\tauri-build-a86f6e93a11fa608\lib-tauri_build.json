{"rustc": 3062648155896360161, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 3932585489389086499, "deps": [[4352886507220678900, "serde_json", false, 17050386825244246145], [4824857623768494398, "cargo_toml", false, 2363863640205458142], [4899080583175475170, "semver", false, 8376593913887195937], [5165059047667588304, "tauri_winres", false, 18228055631076352019], [6913375703034175521, "schemars", false, 9257052003824214268], [7170110829644101142, "json_patch", false, 6664941202271308295], [9293239362693504808, "glob", false, 13377330104911197842], [9689903380558560274, "serde", false, 11812573662551808802], [11207653606310558077, "anyhow", false, 13864934269895721151], [12060164242600251039, "toml", false, 15887119333636123266], [13077543566650298139, "heck", false, 18347787718723399560], [15622660310229662834, "walkdir", false, 2228390448649334760], [16928111194414003569, "dirs", false, 11748574321641554575], [17233053221795943287, "tauri_utils", false, 14556261824533735700]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-a86f6e93a11fa608\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}