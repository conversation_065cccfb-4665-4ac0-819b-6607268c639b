cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=F:\Rust程序测试\aug实现rust重构\src-tauri\target\debug\build\tauri-plugin-log-dfe848acf8860a42\out\tauri-plugin-log-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-log-2.7.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
