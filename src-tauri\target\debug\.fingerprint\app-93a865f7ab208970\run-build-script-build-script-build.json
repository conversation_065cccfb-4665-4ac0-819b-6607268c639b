{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 17390826957038009436], [8427153362654230442, "build_script_build", false, 406349438730838672], [17426757429454803400, "build_script_build", false, 10612869518481610706]], "local": [{"RerunIfChanged": {"output": "debug\\build\\app-93a865f7ab208970\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}