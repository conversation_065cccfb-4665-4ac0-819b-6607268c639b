// 全局变量
let currentMode = 'AI';
let stopwatchStartTime = null;
let stopwatchInterval = null;
let isStopwatchRunning = false;

// DOM元素
const modeRadios = document.querySelectorAll('input[name="mode"]');
const analogInput = document.getElementById('analog-input');
const bInput = document.getElementById('b-input');
const kInput = document.getElementById('k-input');
const setvalueInput = document.getElementById('setvalue-input');
const calculateBtn = document.getElementById('calculate-btn');
const resultDiv = document.getElementById('result');
const autoCopyCheckbox = document.getElementById('auto-copy');
const currentTimeDiv = document.getElementById('current-time');
const stopwatchDiv = document.getElementById('stopwatch');
const stopwatchBtn = document.getElementById('stopwatch-btn');
const fontSizeInput = document.getElementById('font-size-input');
const fontSizeBtn = document.getElementById('font-size-btn');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 模式切换事件
    modeRadios.forEach(radio => {
        radio.addEventListener('change', handleModeChange);
    });

    // 计算按钮事件
    calculateBtn.addEventListener('click', handleCalculate);

    // 字体设置事件
    fontSizeBtn.addEventListener('click', handleFontSizeChange);

    // 计时器事件
    stopwatchBtn.addEventListener('click', handleStopwatchToggle);

    // 自动粘贴事件（临时实现，后续会用Tauri API替换）
    setupAutoPasteEvents();

    // 启动时钟
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
});

// 模式切换处理
function handleModeChange(event) {
    currentMode = event.target.value;
    const container = document.querySelector('.container');
    
    if (currentMode === 'AQ') {
        container.classList.add('mode-aq');
    } else {
        container.classList.remove('mode-aq');
    }
}

// 计算处理（使用Tauri命令）
async function handleCalculate() {
    try {
        const analog = parseFloat(analogInput.value);
        const k = parseFloat(kInput.value);
        const setValue = parseFloat(setvalueInput.value);

        let result;

        if (currentMode === 'AI') {
            const b = parseFloat(bInput.value);
            if (isNaN(analog) || isNaN(k) || isNaN(b) || isNaN(setValue)) {
                throw new Error('无效输入');
            }

            // 调用Tauri AI计算命令
            const response = await window.__TAURI__.core.invoke('calculate_ai', {
                analog: analog,
                k: k,
                b: b,
                setValue: setValue
            });

            if (response.error) {
                throw new Error(response.error);
            }
            result = response.result;
        } else {
            if (isNaN(analog) || isNaN(k) || isNaN(setValue)) {
                throw new Error('无效输入');
            }

            // 调用Tauri AQ计算命令
            const response = await window.__TAURI__.core.invoke('calculate_aq', {
                analog: analog,
                k: k,
                setValue: setValue
            });

            if (response.error) {
                throw new Error(response.error);
            }
            result = response.result;
        }

        resultDiv.textContent = `Result: ${result}`;

        // 自动复制结果
        if (autoCopyCheckbox.checked) {
            try {
                await window.__TAURI__.core.invoke('write_clipboard', { text: result.toString() });
            } catch (err) {
                console.error('复制失败:', err);
                // 降级到浏览器剪贴板API
                navigator.clipboard.writeText(result.toString()).catch(e => {
                    console.error('浏览器剪贴板也失败:', e);
                });
            }
        }

        // 清空输入框
        analogInput.value = '';
        bInput.value = '';

    } catch (error) {
        // 添加错误样式
        analogInput.classList.add('error-input');
        analogInput.value = '无效输入!';
        resultDiv.textContent = 'Result: ';
        console.error('计算错误:', error);

        // 2秒后清除错误状态
        setTimeout(() => {
            analogInput.classList.remove('error-input');
            analogInput.value = '';
        }, 2000);
    }
}

// 字体大小设置
function handleFontSizeChange() {
    const size = parseInt(fontSizeInput.value);
    if (size >= 8 && size <= 24) {
        // 更新所有输入框的字体大小
        analogInput.style.fontSize = size + 'px';
        kInput.style.fontSize = size + 'px';
        bInput.style.fontSize = size + 'px';
        setvalueInput.style.fontSize = size + 'px';

        // 更新其他元素的字体大小
        resultDiv.style.fontSize = size + 'px';
        currentTimeDiv.style.fontSize = size + 'px';
        stopwatchDiv.style.fontSize = size + 'px';

        // 更新标签字体大小
        document.querySelectorAll('label').forEach(label => {
            label.style.fontSize = size + 'px';
        });

        fontSizeInput.value = '';
    } else {
        fontSizeInput.value = '无效大小!';
        setTimeout(() => {
            fontSizeInput.value = '';
        }, 2000);
    }
}

// 时钟更新
function updateCurrentTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');

    const timeString = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    currentTimeDiv.textContent = `当前时间: ${timeString}`;
}

// 计时器处理
function handleStopwatchToggle() {
    if (!isStopwatchRunning) {
        // 开始计时
        stopwatchStartTime = Date.now();
        isStopwatchRunning = true;
        stopwatchBtn.textContent = '停止计时';
        
        stopwatchInterval = setInterval(updateStopwatch, 10);
    } else {
        // 停止计时
        isStopwatchRunning = false;
        stopwatchBtn.textContent = '开始计时';
        clearInterval(stopwatchInterval);
        stopwatchDiv.textContent = '计时器: 00:00:00.000';
    }
}

// 更新计时器显示
function updateStopwatch() {
    if (isStopwatchRunning) {
        const elapsed = Date.now() - stopwatchStartTime;
        const hours = Math.floor(elapsed / 3600000);
        const minutes = Math.floor((elapsed % 3600000) / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        const milliseconds = elapsed % 1000;
        
        const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
        stopwatchDiv.textContent = `计时器: ${timeString}`;
    }
}

// 自动粘贴设置（使用Tauri剪贴板命令）
function setupAutoPasteEvents() {
    const autoPasteInputs = document.querySelectorAll('.auto-paste-input');

    autoPasteInputs.forEach(input => {
        input.addEventListener('click', async function() {
            try {
                const text = await window.__TAURI__.core.invoke('read_clipboard');
                if (text) {
                    this.value = text;
                }
            } catch (err) {
                console.error('读取剪贴板失败:', err);
                // 降级到浏览器剪贴板API
                try {
                    const text = await navigator.clipboard.readText();
                    if (text) {
                        this.value = text;
                    }
                } catch (e) {
                    console.error('浏览器剪贴板也失败:', e);
                }
            }
        });
    });
}
