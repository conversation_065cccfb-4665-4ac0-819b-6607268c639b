{"rustc": 3062648155896360161, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2225463790103693989, "path": 7599337587338048966, "deps": [[7312356825837975969, "crc32fast", false, 1816930789849461294], [7636735136738807108, "miniz_oxide", false, 14587121075228951649]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-6b81ea12db47d50f\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}