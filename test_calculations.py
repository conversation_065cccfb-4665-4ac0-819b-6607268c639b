#!/usr/bin/env python3
"""
测试脚本：验证Rust重构版本的计算逻辑与原Python版本一致
"""

def test_ai_calculation():
    """测试AI模式计算"""
    # 原Python逻辑
    analog = 100.0
    k = 2.0
    b = 10.0
    set_value = 50.0
    
    x = (analog - b) / k  # (100 - 10) / 2 = 45
    k1 = (set_value - b) / x  # (50 - 10) / 45 = 0.8889
    k3 = (2 * k) - k1  # (2 * 2) - 0.8889 = 3.1111
    
    expected_result = round(k3, 4)
    print(f"AI模式测试:")
    print(f"输入: analog={analog}, k={k}, b={b}, set_value={set_value}")
    print(f"预期结果: {expected_result}")
    print(f"计算过程: x={x}, k1={k1}, k3={k3}")
    print()

def test_aq_calculation():
    """测试AQ模式计算"""
    # 原Python逻辑
    analog = 100.0
    k = 2.0
    set_value = 50.0
    
    x = analog / k  # 100 / 2 = 50
    k9 = set_value / x  # 50 / 50 = 1.0
    
    expected_result = round(k9, 6)
    print(f"AQ模式测试:")
    print(f"输入: analog={analog}, k={k}, set_value={set_value}")
    print(f"预期结果: {expected_result}")
    print(f"计算过程: x={x}, k9={k9}")
    print()

def test_edge_cases():
    """测试边界情况"""
    print("边界情况测试:")
    
    # 测试k=0的情况
    print("1. k=0时应该返回错误")
    
    # 测试除零情况
    print("2. 中间计算结果为0时应该返回错误")
    
    # 测试NaN输入
    print("3. NaN输入时应该返回错误")
    print()

if __name__ == "__main__":
    print("=== 计算逻辑验证测试 ===")
    print()
    
    test_ai_calculation()
    test_aq_calculation()
    test_edge_cases()
    
    print("请在Tauri应用中使用相同的输入值进行测试，验证结果是否一致。")
