body {
    margin: 0;
    padding: 20px;
    font-family: Arial, sans-serif;
    font-size: 13px;
    background-color: #f0f0f0;
}

.container {
    max-width: 400px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 16px;
    color: #333;
}

.mode-selection {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    justify-content: center;
}

.mode-selection label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    cursor: pointer;
}

.input-section {
    margin-bottom: 20px;
}

.input-section label {
    display: block;
    margin-bottom: 5px;
    font-size: 13px;
    font-weight: bold;
}

.input-section input {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 13px;
    box-sizing: border-box;
}

.calculation-section {
    margin-bottom: 20px;
}

.prompt {
    font-size: 13px;
    margin-bottom: 10px;
    color: #666;
}

.result {
    font-size: 13px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 20px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
    font-size: 13px;
    cursor: pointer;
}

button {
    width: 100%;
    padding: 10px;
    font-size: 13px;
    background-color: #007acc;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 10px;
}

button:hover {
    background-color: #005a9e;
}

.time-section {
    margin-bottom: 20px;
}

.time-section div {
    font-size: 13px;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.font-section {
    display: flex;
    gap: 10px;
    align-items: center;
}

.font-section input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 13px;
}

.font-section button {
    flex: 2;
    margin-bottom: 0;
}

/* 隐藏AQ模式下的b_value输入 */
.mode-aq #b-input,
.mode-aq #b-label {
    display: none;
}

/* 字体大小动态调整 */
.dynamic-font {
    font-size: inherit !important;
}

.dynamic-font input,
.dynamic-font button,
.dynamic-font label,
.dynamic-font div {
    font-size: inherit !important;
}

/* 输入框焦点样式 */
input:focus {
    outline: none;
    border-color: #007acc;
    box-shadow: 0 0 5px rgba(0, 122, 204, 0.3);
}

/* 按钮激活状态 */
button:active {
    background-color: #004080;
    transform: translateY(1px);
}

/* 计时器特殊样式 */
#stopwatch {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    text-align: center;
    background-color: #e8f4fd;
    border-color: #007acc;
}

/* 结果显示特殊样式 */
.result {
    font-weight: bold;
    color: #333;
}

/* 错误状态样式 */
.error-input {
    border-color: #ff4444 !important;
    background-color: #fff5f5 !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        margin: 10px;
        padding: 15px;
    }

    .font-section {
        flex-direction: column;
    }

    .font-section button {
        width: 100%;
    }

    .mode-selection {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
}
