{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 7337875010287313860, "deps": [[503635761244294217, "regex", false, 10048965305309374261], [1200537532907108615, "url<PERSON><PERSON>n", false, 7797225855875416119], [1678291836268844980, "brotli", false, 5614173258036726898], [4071963112282141418, "serde_with", false, 13748675956920343328], [4352886507220678900, "serde_json", false, 108169988327864308], [4537297827336760846, "thiserror", false, 8699437905799808735], [4899080583175475170, "semver", false, 14830861494747423424], [5404511084185685755, "url", false, 5908014307577041281], [6606131838865521726, "ctor", false, 7052709996124253240], [7170110829644101142, "json_patch", false, 537521636384584670], [9010263965687315507, "http", false, 13184972111510878777], [9293239362693504808, "glob", false, 9378658457654588425], [9689903380558560274, "serde", false, 8940050712156267858], [11207653606310558077, "anyhow", false, 10727761341698528648], [11989259058781683633, "dunce", false, 8342769553454248279], [12060164242600251039, "toml", false, 388926847745184008], [13066042571740262168, "log", false, 9752324521076004568], [15267671913832104935, "uuid", false, 11990587744823580883], [15622660310229662834, "walkdir", false, 18263676186705757965], [15932120279885307830, "memchr", false, 5550596881669741694], [17146114186171651583, "infer", false, 2549840721435850304], [17183029615630212089, "serde_untagged", false, 985026737380467144], [17186037756130803222, "phf", false, 17115412123626422684]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-b725960ebe2d37ed\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}