{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 1086681688600947206, "deps": [[2013030631243296465, "webview2_com", false, 18250755667997916887], [4143744114649553716, "raw_window_handle", false, 14790246364505868691], [4352886507220678900, "serde_json", false, 108169988327864308], [4537297827336760846, "thiserror", false, 8699437905799808735], [5404511084185685755, "url", false, 5908014307577041281], [7606335748176206944, "dpi", false, 8397960415140041327], [9010263965687315507, "http", false, 13184972111510878777], [9689903380558560274, "serde", false, 8940050712156267858], [14585479307175734061, "windows", false, 44454078113342564], [16727543399706004146, "cookie", false, 10648612767976272956], [17233053221795943287, "tauri_utils", false, 11282211087323182116], [18010483002580779355, "build_script_build", false, 10597446665251338247]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-513f60f6690afe33\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}