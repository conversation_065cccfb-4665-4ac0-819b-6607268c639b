# AI/AQ_Ktool_V3.4 功能测试清单

## 基本界面功能
- [ ] 应用启动正常，窗口大小为400x600
- [ ] 标题显示为"AI/AQ_Ktool_V3.4"
- [ ] 所有界面元素正确显示

## 模式切换功能
- [ ] AI模式默认选中
- [ ] 切换到AQ模式时，b_value输入框隐藏
- [ ] 切换回AI模式时，b_value输入框显示

## 计算功能测试

### AI模式计算
测试用例：analog=100, k=2, b=10, set_value=50
- [ ] 输入上述数值
- [ ] 点击计算按钮
- [ ] 结果应显示：Result: 3.1111
- [ ] 计算后analog和b输入框自动清空

### AQ模式计算
测试用例：analog=100, k=2, set_value=50
- [ ] 切换到AQ模式
- [ ] 输入上述数值
- [ ] 点击计算按钮
- [ ] 结果应显示：Result: 1.0
- [ ] 计算后analog输入框自动清空

### 错误处理
- [ ] 输入无效数值时显示"无效输入!"
- [ ] k=0时正确处理错误
- [ ] 空输入时正确处理错误

## 剪贴板功能
- [ ] 点击自动粘贴输入框时，剪贴板内容自动填入
- [ ] 自动复制结果功能正常工作
- [ ] 可以取消自动复制功能

## 时间和计时器功能
- [ ] 实时时钟正确显示当前时间（格式：yyyy-MM-dd HH:mm:ss）
- [ ] 计时器初始显示"计时器: 00:00:00.000"
- [ ] 点击"开始计时"按钮，计时器开始工作
- [ ] 按钮文字变为"停止计时"
- [ ] 点击"停止计时"按钮，计时器停止并重置
- [ ] 按钮文字变回"开始计时"

## 字体调整功能
- [ ] 输入有效字体大小（8-24），界面字体正确调整
- [ ] 输入无效字体大小时显示"无效大小!"
- [ ] 错误信息2秒后自动清除

## 界面响应性
- [ ] 窗口可以正常调整大小
- [ ] 界面元素布局合理
- [ ] 按钮点击有视觉反馈
- [ ] 输入框焦点状态正确显示

## 与原应用对比
- [ ] 功能完整性：所有原应用功能都已实现
- [ ] 计算准确性：计算结果与原应用一致
- [ ] 用户体验：操作流程与原应用相似
- [ ] 界面外观：视觉效果接近原应用

## 性能测试
- [ ] 应用启动速度合理
- [ ] 计算响应速度快
- [ ] 内存使用合理
- [ ] 无明显卡顿或延迟

## 错误恢复
- [ ] 输入错误后可以正常继续使用
- [ ] 计算错误不影响其他功能
- [ ] 应用稳定，不会崩溃

---

## 测试结果总结
- 通过项目数：___/___
- 发现问题：
  1. 
  2. 
  3. 

## 改进建议
1. 
2. 
3. 
