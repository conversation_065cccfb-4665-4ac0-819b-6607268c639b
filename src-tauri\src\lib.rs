use serde::{Deserialize, Serialize};
use arboard::Clipboard;

#[derive(Serialize, Deserialize)]
struct CalculationResult {
    result: f64,
    error: Option<String>,
}

// AI模式计算命令
#[tauri::command]
fn calculate_ai(analog: f64, k: f64, b: f64, set_value: f64) -> CalculationResult {
    if analog.is_nan() || k.is_nan() || b.is_nan() || set_value.is_nan() {
        return CalculationResult {
            result: 0.0,
            error: Some("无效输入".to_string()),
        };
    }

    if k == 0.0 {
        return CalculationResult {
            result: 0.0,
            error: Some("k值不能为0".to_string()),
        };
    }

    let x = (analog - b) / k;
    if x == 0.0 {
        return CalculationResult {
            result: 0.0,
            error: Some("计算中间值x为0".to_string()),
        };
    }

    let k1 = (set_value - b) / x;
    let k3 = (2.0 * k) - k1;

    CalculationResult {
        result: (k3 * 10000.0).round() / 10000.0, // 保留4位小数
        error: None,
    }
}

// AQ模式计算命令
#[tauri::command]
fn calculate_aq(analog: f64, k: f64, set_value: f64) -> CalculationResult {
    if analog.is_nan() || k.is_nan() || set_value.is_nan() {
        return CalculationResult {
            result: 0.0,
            error: Some("无效输入".to_string()),
        };
    }

    if k == 0.0 {
        return CalculationResult {
            result: 0.0,
            error: Some("k值不能为0".to_string()),
        };
    }

    let x = analog / k;
    if x == 0.0 {
        return CalculationResult {
            result: 0.0,
            error: Some("计算中间值x为0".to_string()),
        };
    }

    let k9 = set_value / x;

    CalculationResult {
        result: (k9 * 1000000.0).round() / 1000000.0, // 保留6位小数
        error: None,
    }
}

// 读取剪贴板内容
#[tauri::command]
fn read_clipboard() -> Result<String, String> {
    let mut clipboard = Clipboard::new().map_err(|e| format!("无法访问剪贴板: {}", e))?;
    clipboard.get_text().map_err(|e| format!("读取剪贴板失败: {}", e))
}

// 复制内容到剪贴板
#[tauri::command]
fn write_clipboard(text: String) -> Result<(), String> {
    let mut clipboard = Clipboard::new().map_err(|e| format!("无法访问剪贴板: {}", e))?;
    clipboard.set_text(text).map_err(|e| format!("复制到剪贴板失败: {}", e))
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
  tauri::Builder::default()
    .invoke_handler(tauri::generate_handler![calculate_ai, calculate_aq, read_clipboard, write_clipboard])
    .setup(|app| {
      if cfg!(debug_assertions) {
        app.handle().plugin(
          tauri_plugin_log::Builder::default()
            .level(log::LevelFilter::Info)
            .build(),
        )?;
      }
      Ok(())
    })
    .run(tauri::generate_context!())
    .expect("error while running tauri application");
}
