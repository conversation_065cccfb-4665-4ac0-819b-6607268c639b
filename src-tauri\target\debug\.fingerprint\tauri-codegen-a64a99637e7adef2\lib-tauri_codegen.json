{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 6181833192162919371, "deps": [[373107762698212489, "proc_macro2", false, 17105446800078890304], [1678291836268844980, "brotli", false, 2373815210596436151], [4352886507220678900, "serde_json", false, 17050386825244246145], [4537297827336760846, "thiserror", false, 3505554297927552545], [4899080583175475170, "semver", false, 8376593913887195937], [5404511084185685755, "url", false, 13164318066210883623], [7170110829644101142, "json_patch", false, 6664941202271308295], [7392050791754369441, "ico", false, 13509851281603847150], [9689903380558560274, "serde", false, 11812573662551808802], [9857275760291862238, "sha2", false, 16177330049264162843], [12687914511023397207, "png", false, 8211759409946285821], [13077212702700853852, "base64", false, 3704075103290783005], [15267671913832104935, "uuid", false, 6095326837184388551], [15622660310229662834, "walkdir", false, 2228390448649334760], [17233053221795943287, "tauri_utils", false, 14556261824533735700], [17332570067994900305, "syn", false, 2706061181772389358], [17990358020177143287, "quote", false, 15717735471653760071]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-a64a99637e7adef2\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}