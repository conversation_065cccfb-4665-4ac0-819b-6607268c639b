{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 12726435800596691289, "deps": [[376837177317575824, "softbuffer", false, 3635080010219041606], [1825855694502115139, "tao", false, 14163965158001168645], [2013030631243296465, "webview2_com", false, 18250755667997916887], [3722963349756955755, "once_cell", false, 12072434053143140140], [4143744114649553716, "raw_window_handle", false, 14790246364505868691], [5404511084185685755, "url", false, 5908014307577041281], [8558698349995473911, "wry", false, 11511358445910994638], [9010263965687315507, "http", false, 13184972111510878777], [9952368442187680820, "build_script_build", false, 13497746306797043883], [13066042571740262168, "log", false, 9752324521076004568], [14585479307175734061, "windows", false, 44454078113342564], [17233053221795943287, "tauri_utils", false, 11282211087323182116], [18010483002580779355, "tauri_runtime", false, 7621909874765641470]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-d997d052b81a8a14\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}