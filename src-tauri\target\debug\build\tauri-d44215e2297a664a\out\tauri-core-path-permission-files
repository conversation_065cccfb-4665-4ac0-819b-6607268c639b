["\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\path\\autogenerated\\default.toml"]