["\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\available_monitors.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\center.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\close.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\create.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\current_monitor.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\cursor_position.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\destroy.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\get_all_windows.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\hide.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\inner_position.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\inner_size.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\internal_toggle_maximize.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_always_on_top.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_closable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_decorated.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_focused.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_fullscreen.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_maximizable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_maximized.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_minimizable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_minimized.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_resizable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\is_visible.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\maximize.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\minimize.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\monitor_from_point.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\outer_position.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\outer_size.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\primary_monitor.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\request_user_attention.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\scale_factor.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_bottom.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_always_on_top.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_background_color.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_badge_count.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_badge_label.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_closable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_content_protected.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_grab.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_icon.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_position.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_cursor_visible.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_decorations.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_effects.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_focus.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_focusable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_fullscreen.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_icon.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_ignore_cursor_events.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_max_size.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_maximizable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_min_size.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_minimizable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_overlay_icon.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_position.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_progress_bar.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_resizable.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_shadow.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_simple_fullscreen.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_size.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_size_constraints.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_skip_taskbar.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_theme.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_title.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_title_bar_style.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\set_visible_on_all_workspaces.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\show.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\start_dragging.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\start_resize_dragging.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\theme.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\title.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\toggle_maximize.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\unmaximize.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\commands\\unminimize.toml", "\\\\?\\F:\\Rust程序测试\\aug实现rust重构\\src-tauri\\target\\debug\\build\\tauri-d44215e2297a664a\\out\\permissions\\window\\autogenerated\\default.toml"]